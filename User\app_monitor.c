/**
 ******************************************************************************
 * @file    app_monitor.c
 * <AUTHOR>
 * @version V2.0.0
 * @date    2025-07-03
 * @brief   Module for monitoring INA226 using optimized EXTI+DMA approach.
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include "app_monitor.h"
#include "mod_ina226.h"
#include "app_util.h"
#include "FreeRTOS.h"
#include "queue.h"
#include "task.h"

/* Private define ------------------------------------------------------------*/
#define PROCESSED_DATA_QUEUE_LENGTH 50
#define MONITOR_TASK_STACK_SIZE     512
#define MONITOR_TASK_PRIORITY       3
#define RAW_SAMPLE_BUFFER_SIZE      100

/* Private variables ---------------------------------------------------------*/
static QueueHandle_t processed_data_queue = NULL;
static TaskHandle_t monitor_task_handle = NULL;
static uint16_t raw_sample_buffer[RAW_SAMPLE_BUFFER_SIZE];
static bool monitor_running = false;

/* Private function prototypes -----------------------------------------------*/
static void Monitor_Task(void *pvParameters);
static void ProcessRawSamples(uint16_t *samples, uint32_t count);
static float ConvertToShuntVoltage(uint16_t raw_value);
static float ConvertToCurrent(float shunt_mv);

/* Public functions ----------------------------------------------------------*/

/**
 * @brief Initializes the monitoring application module with optimized EXTI+DMA.
 * @retval true if initialization is successful, false otherwise.
 */
bool App_Monitor_Init(void)
{
    logi("=== App Monitor Init (Optimized) ===\r\n");

    // Initialize INA226 in optimized mode
    if (!INA226_InitOptimized()) {
        logi("INA226 optimized initialization failed!\r\n");
        return false;
    } 

    // Create a queue to hold processed INA226 data
    processed_data_queue = xQueueCreate(PROCESSED_DATA_QUEUE_LENGTH, sizeof(INA226_Data_t));
    if (processed_data_queue == NULL) {
        logi("Failed to create processed data queue.\r\n");
        return false;
    }
    logi("Processed data queue created.\r\n");

    // Create the monitoring task
    if (xTaskCreate(Monitor_Task,
                    "MonitorOpt",
                    MONITOR_TASK_STACK_SIZE,
                    NULL,
                    MONITOR_TASK_PRIORITY,
                    &monitor_task_handle) != pdPASS) {
        logi("Failed to create monitor task.\r\n");
        return false;
    }
    logi("Monitor task created.\r\n");

    // Start optimized data acquisition
    if (!INA226_StartOptimizedAcquisition()) {
        logi("Failed to start optimized acquisition!\r\n");
        return false;
    }
    logi("Optimized data acquisition started.\r\n");

    monitor_running = true;
    return true;
}

/**
 * @brief Retrieves the latest processed INA226 data from the queue.
 * @param data Pointer to a structure to store the data.
 * @param timeout_ms Timeout in milliseconds to wait for data.
 * @retval true if data was received, false otherwise.
 */
bool App_Monitor_Get_Data(INA226_Data_t *data, uint32_t timeout_ms)
{
    if (data == NULL || processed_data_queue == NULL) {
        return false;
    }

    if (xQueueReceive(processed_data_queue, data, pdMS_TO_TICKS(timeout_ms)) == pdPASS) {
        return true;
    }

    return false;
}

/**
 * @brief Stops the monitoring application.
 * @retval None
 */
void App_Monitor_Stop(void)
{
    monitor_running = false;
    INA226_StopOptimizedAcquisition();
    logi("App Monitor stopped.\r\n");
}

/**
 * @brief Gets the monitoring status.
 * @retval true if monitoring is running, false otherwise.
 */
bool App_Monitor_IsRunning(void)
{
    return monitor_running;
}

/* Private functions ---------------------------------------------------------*/

/**
 * @brief Monitor task that processes optimized INA226 data
 */
static void Monitor_Task(void *pvParameters)
{
    uint32_t cycle_count = 0;
    uint32_t total_samples = 0;

    logi("Monitor task started (optimized mode).\r\n");

    while (1)
    {
        if (monitor_running)
        {
            // Check for new raw data from EXTI+DMA
            if (INA226_IsNewDataAvailable())
            {
                uint32_t samples_read = INA226_GetLatestData(raw_sample_buffer, RAW_SAMPLE_BUFFER_SIZE);

                if (samples_read > 0)
                {
                    total_samples += samples_read;
                    cycle_count++;

                    // Process the raw samples
                    ProcessRawSamples(raw_sample_buffer, samples_read);

                    // Log statistics every 100 cycles
                    if ((cycle_count % 100) == 0)
                    {
                        logi("Cycle %lu: Processed %lu total samples\r\n", cycle_count, total_samples);
                    }
                }
            }

            // Task delay - check for new data every 5ms
            vTaskDelay(pdMS_TO_TICKS(5));
        }
        else
        {
            // Monitor stopped, wait longer
            vTaskDelay(pdMS_TO_TICKS(100));
        }
    }
}

/**
 * @brief Process raw samples and generate processed data
 * @param samples: Pointer to raw sample buffer
 * @param count: Number of samples
 * @retval None
 */
static void ProcessRawSamples(uint16_t *samples, uint32_t count)
{
    if (samples == NULL || count == 0) return;

    // Calculate statistics from the batch
    float voltage_sum = 0.0f;
    float current_sum = 0.0f;
    float power_sum = 0.0f;
    float shunt_sum = 0.0f;

    float min_current = 999.0f;
    float max_current = -999.0f;

    for (uint32_t i = 0; i < count; i++)
    {
        float shunt_mv = ConvertToShuntVoltage(samples[i]);
        float current_a = ConvertToCurrent(shunt_mv);

        // For this example, assume bus voltage is 3.3V
        // In real application, you could read this from INA226 bus voltage register
        float voltage_v = 3.3f;
        float power_w = current_a * voltage_v;

        // Accumulate for averages
        voltage_sum += voltage_v;
        current_sum += current_a;
        power_sum += power_w;
        shunt_sum += shunt_mv;

        // Track min/max current
        if (current_a < min_current) min_current = current_a;
        if (current_a > max_current) max_current = current_a;
    }

    // Create processed data structure with averages
    INA226_Data_t processed_data;
    processed_data.voltage_V = voltage_sum / count;
    processed_data.current_A = current_sum / count;
    processed_data.power_W = power_sum / count;
    processed_data.shunt_voltage_mV = shunt_sum / count;

    // Send processed data to queue (non-blocking)
    if (processed_data_queue != NULL)
    {
        xQueueSend(processed_data_queue, &processed_data, 0);
    }

    // Log detailed statistics occasionally
    static uint32_t log_counter = 0;
    if ((++log_counter % 50) == 0)
    {
        logi("Batch stats: %lu samples, I: %.6f-%.6fA (avg: %.6f), P: %.3fW\r\n",
             count, min_current, max_current, processed_data.current_A, processed_data.power_W);
    }
}

/**
 * @brief Convert raw value to shunt voltage
 * @param raw_value: Raw 16-bit value from INA226
 * @retval Shunt voltage in millivolts
 */
static float ConvertToShuntVoltage(uint16_t raw_value)
{
    // INA226 shunt voltage LSB is 2.5µV per bit
    int16_t signed_value = (int16_t)raw_value;
    return signed_value * 0.0025f; // Convert to mV
}

/**
 * @brief Convert shunt voltage to current
 * @param shunt_mv: Shunt voltage in millivolts
 * @retval Current in amperes
 */
static float ConvertToCurrent(float shunt_mv)
{
    // I = V / R, where R = 0.75Ω (750mΩ)
    return (shunt_mv / 1000.0f) / INA226_SHUNT_RESISTANCE_OHMS;
}
